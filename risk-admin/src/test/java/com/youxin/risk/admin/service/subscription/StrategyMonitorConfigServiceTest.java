package com.youxin.risk.admin.service.subscription;

import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.SLSConfig;
import com.youxin.risk.commons.utils.SLSLinkGenerator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * 策略监控配置服务测试类
 * 重构后的版本，修复了MockedStatic相关问题
 *
 * <AUTHOR> Assistant
 */
@RunWith(MockitoJUnitRunner.class)
public class StrategyMonitorConfigServiceTest {

    @InjectMocks
    private StrategyMonitorConfigService strategyMonitorConfigService;

    private MonitorConfig dynamicMonitor;
    private MonitorConfig staticMonitor;
    private MockedStatic<SLSLinkGenerator> mockedSLSLinkGenerator;

    @Before
    public void setUp() {
        // 准备测试数据 - 动态监控配置
        dynamicMonitor = new MonitorConfig();
        dynamicMonitor.setId("dashboard-1747904265305-468192");
        dynamicMonitor.setName("动态监控大盘");
        dynamicMonitor.setEventCode("ApiVerify");
        dynamicMonitor.setUseDynamicLink(true);
        dynamicMonitor.setFrequencyMinutes(30);
        dynamicMonitor.setTotalPushes(5);

        // 设置 SLS 配置
        SLSConfig slsConfig = new SLSConfig();
        slsConfig.setDashboardName("dashboard-1747904265305-468192");

        List<Map<String, String>> token = new ArrayList<>();
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("key", "date");
        tokenMap.put("value", "60");
        token.add(tokenMap);
        slsConfig.setToken(token);

        List<Map<String, String>> extensions = new ArrayList<>();
        Map<String, String> extensionMap = new HashMap<>();
        extensionMap.put("autoFresh", "30s");
        extensions.add(extensionMap);
        slsConfig.setExtensions(extensions);

        dynamicMonitor.setSlsConfig(slsConfig);

        // 准备测试数据 - 静态监控配置
        staticMonitor = new MonitorConfig();
        staticMonitor.setId("sls-002");
        staticMonitor.setName("静态监控大盘");
        staticMonitor.setUrl("http://sls.example.com/d/xxx/static");
        staticMonitor.setUseDynamicLink(false);
    }

    @Test
    public void testGenerateMonitorLink_DynamicLink_Success() {
        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedLink = "https://sls.console.aliyun.com/lognext/project/risk-service-logs/dashboard/dashboard-1747904265305-468192?slsRegion=cn-beijing&sls_ticket=test-ticket";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    eq("dashboard-1747904265305-468192"),
                    anyList(),
                    anyList()))
                    .thenReturn(expectedLink);

            // 执行测试
            String result = strategyMonitorConfigService.generateMonitorLink(dynamicMonitor);

            // 验证结果
            assertEquals(expectedLink, result);
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    eq("dashboard-1747904265305-468192"),
                    anyList(),
                    anyList()));
        }
    }

    @Test
    public void testGenerateMonitorLink_DynamicLink_Fallback() {
        // 设置动态监控也有静态URL作为备用
        dynamicMonitor.setUrl("http://sls.example.com/d/xxx/fallback");

        // Mock SLSLinkGenerator 返回null
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    anyString(),
                    anyList(),
                    anyList()))
                    .thenReturn(null);

            // 执行测试
            String result = strategyMonitorConfigService.generateMonitorLink(dynamicMonitor);

            // 验证结果 - 应该回退到静态URL
            assertEquals("http://sls.example.com/d/xxx/fallback", result);
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    anyString(),
                    anyList(),
                    anyList()));
        }
    }

    @Test
    public void testGenerateMonitorLink_StaticLink() {
        // 执行测试
        String result = strategyMonitorConfigService.generateMonitorLink(staticMonitor);

        // 验证结果
        assertEquals("http://sls.example.com/d/xxx/static", result);
    }

    @Test
    public void testGenerateMonitorLink_NullMonitor() {
        // 执行测试
        String result = strategyMonitorConfigService.generateMonitorLink(null);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testBatchGenerateMonitorLinks() {
        List<MonitorConfig> monitors = Arrays.asList(dynamicMonitor, staticMonitor);

        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedDynamicLink = "https://sls.console.aliyun.com/dynamic-link";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    eq("dashboard-1747904265305-468192"),
                    anyList(),
                    anyList()))
                    .thenReturn(expectedDynamicLink);

            // 执行测试
            Map<String, String> result = strategyMonitorConfigService.batchGenerateMonitorLinks(monitors);

            // 验证结果
            assertEquals(2, result.size());
            assertEquals(expectedDynamicLink, result.get("dashboard-1747904265305-468192"));
            assertEquals("http://sls.example.com/d/xxx/static", result.get("sls-002"));
        }
    }

    @Test
    public void testRefreshMonitorLink_Success() {
        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedLink = "https://sls.console.aliyun.com/refreshed-link";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"))
                    .thenReturn(expectedLink);

            // 执行测试
            String result = strategyMonitorConfigService.refreshMonitorLink("ApiVerify");

            // 验证结果
            assertEquals(expectedLink, result);
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"));
        }
    }

    @Test
    public void testRefreshMonitorLink_EmptyEventCode() {
        // 执行测试
        String result = strategyMonitorConfigService.refreshMonitorLink("");

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testRefreshMonitorLink_NullEventCode() {
        // 执行测试
        String result = strategyMonitorConfigService.refreshMonitorLink(null);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testRefreshMonitorLink_GenerationFailed() {
        // Mock SLSLinkGenerator 返回null
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode(anyString()))
                    .thenReturn(null);

            // 执行测试
            String result = strategyMonitorConfigService.refreshMonitorLink("InvalidEventCode");

            // 验证结果
            assertNull(result);
        }
    }

    @Test
    public void testMonitorConfig_IsDynamicLinkEnabled() {
        // 测试动态链接启用
        assertTrue(dynamicMonitor.isDynamicLinkEnabled());

        // 测试静态链接
        assertFalse(staticMonitor.isDynamicLinkEnabled());

        // 测试没有eventCode的情况
        MonitorConfig noEventCodeMonitor = new MonitorConfig();
        noEventCodeMonitor.setUseDynamicLink(true);
        assertFalse(noEventCodeMonitor.isDynamicLinkEnabled());

        // 测试useDynamicLink为false的情况
        MonitorConfig disabledDynamicMonitor = new MonitorConfig();
        disabledDynamicMonitor.setEventCode("TestEvent");
        disabledDynamicMonitor.setUseDynamicLink(false);
        assertFalse(disabledDynamicMonitor.isDynamicLinkEnabled());
    }

    @Test
    public void testMonitorConfig_SLSConfig() {
        // 测试SLS配置的设置和获取
        assertNotNull("动态监控应该有SLS配置", dynamicMonitor.getSlsConfig());
        assertEquals("dashboard-1747904265305-468192", dynamicMonitor.getSlsConfig().getDashboardName());

        // 测试token配置
        List<Map<String, String>> tokens = dynamicMonitor.getSlsConfig().getToken();
        assertNotNull("Token配置不应该为空", tokens);
        assertEquals(1, tokens.size());
        assertEquals("date", tokens.get(0).get("key"));
        assertEquals("60", tokens.get(0).get("value"));

        // 测试extensions配置
        List<Map<String, String>> extensions = dynamicMonitor.getSlsConfig().getExtensions();
        assertNotNull("Extensions配置不应该为空", extensions);
        assertEquals(1, extensions.size());
        assertEquals("30s", extensions.get(0).get("autoFresh"));

        // 测试静态监控没有SLS配置
        assertNull("静态监控不应该有SLS配置", staticMonitor.getSlsConfig());
    }

    @Test
    public void testMonitorConfig_EffectiveConfiguration() {
        // 测试有效的推送频率配置
        assertEquals(30, dynamicMonitor.getEffectiveFrequencyMinutes(60));
        assertEquals(60, staticMonitor.getEffectiveFrequencyMinutes(60)); // 使用默认值

        // 测试有效的总推送次数配置
        assertEquals(5, dynamicMonitor.getEffectiveTotalPushes(3));
        assertEquals(3, staticMonitor.getEffectiveTotalPushes(3)); // 使用默认值
    }

    @Test
    public void testGenerateMonitorLink_WithSLSConfig() {
        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedLink = "https://sls.console.aliyun.com/test-sls-config-link";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    eq("dashboard-1747904265305-468192"),
                    argThat(tokens -> tokens.size() == 1 && "date".equals(tokens.get(0).get("key"))),
                    argThat(extensions -> extensions.size() == 1 && "30s".equals(extensions.get(0).get("autoFresh")))))
                    .thenReturn(expectedLink);

            // 执行测试
            String result = strategyMonitorConfigService.generateMonitorLink(dynamicMonitor);

            // 验证结果
            assertEquals(expectedLink, result);

            // 验证调用参数
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByConfig(
                    eq("dashboard-1747904265305-468192"),
                    argThat(tokens -> tokens.size() == 1 && "date".equals(tokens.get(0).get("key"))),
                    argThat(extensions -> extensions.size() == 1 && "30s".equals(extensions.get(0).get("autoFresh")))));
        }
    }

    @Test
    public void testGenerateMonitorLink_NoSLSConfig() {
        // 创建一个没有SLS配置的动态监控
        MonitorConfig noSLSConfigMonitor = new MonitorConfig();
        noSLSConfigMonitor.setId("test-monitor");
        noSLSConfigMonitor.setName("测试监控");
        noSLSConfigMonitor.setEventCode("TestEvent");
        noSLSConfigMonitor.setUseDynamicLink(true);
        noSLSConfigMonitor.setUrl("http://fallback.url");
        // 没有设置 slsConfig

        // 执行测试
        String result = strategyMonitorConfigService.generateMonitorLink(noSLSConfigMonitor);

        // 验证结果 - 应该回退到静态URL
        assertEquals("http://fallback.url", result);
    }
}
